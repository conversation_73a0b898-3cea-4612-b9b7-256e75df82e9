{"extends": "./.nuxt/tsconfig.json", "compilerOptions": {"baseUrl": ".", "module": "nodenext", "moduleResolution": "nodenext", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "types": ["nuxt", "vitest/globals"], "paths": {"@/*": ["src/*"], "~/*": ["src/*"], "#imports": ["./.nuxt/imports.d.ts"], "#components": ["./.nuxt/components.d.ts"]}}, "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "tests/**/*.ts"]}