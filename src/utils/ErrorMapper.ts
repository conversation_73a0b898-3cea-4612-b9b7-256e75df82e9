import type { FetchError } from 'ofetch'

/**
 * Маппер ошибое при авторизации.
 *
 * @param error ошибка
 */
export function mapLoginError(error: unknown): string {
  const err = error as FetchError
  const status = err?.response?.status

  // TODO: Использовать ответы от сервера
  switch (status) {
    case 400:
      return 'Неверный email или пароль.'
    case 404:
      return 'Пользователь не найден или заблокирован.'
    default:
      return 'Произошла ошибка на сервере.'
  }
}
